package channel

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	internalChannel "git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

type APIRespChannel struct {
	Name      string            `json:"name"`
	RootID    int               `json:"root_id"`
	CurrentID int               `json:"current_id"`
	Children  []*APIRespChannel `json:"children"`
}

// 代理到internal层的读取范围函数
func channelReadScope(c *gin.Context) func(tx *gorm.DB) *gorm.DB {
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	return internalChannel.ChannelReadScope(
		user.ID,
		user.CanPrivileged(acl.Channel, acl.Read),
		privileged,
	)
}

// 检查渠道ID是否在用户的渠道树中
func isInChannelTree(c *gin.Context) bool {
	channelId := cast.ToUint64(c.Param("id"))
	user := api.CurrentUser(c)

	return internalChannel.IsUserInChannelTree(
		user.ID,
		channelId,
		user.CanPrivileged(acl.Channel, acl.Write),
	)
}

// GetChannel 获取渠道信息
func GetChannel(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 调用internal层获取渠道信息
	userChannel, err := internalChannel.GetChannelByID(id, channelReadScope(c))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, userChannel)
}

// ModifyChannel 修改渠道信息
func ModifyChannel(c *gin.Context) {
	if !isInChannelTree(c) {
		c.AbortWithError(http.StatusForbidden, internalChannel.ErrChannelNotInChannelTree)
		return
	}

	id := cast.ToUint64(c.Param("id"))

	// 调用internal层修改渠道信息
	err := internalChannel.ModifyChannelByID(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	GetChannel(c)
}

// GetChannelList 获取渠道列表
func GetChannelList(c *gin.Context) {
	channelQuery := c.Query("channel")
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	cosy.Core[model.Channel](c).
		GormScope(func(tx *gorm.DB) *gorm.DB {
			// 预加载用户信息
			tx = tx.Preload("User").Preload("ParentUser")

			if channelQuery != "" {
				// 通过关联的用户名进行搜索
				tx = tx.Joins("JOIN users ON users.id = channels.user_id").
					Where("users.name LIKE ?", "%"+channelQuery+"%")
			}

			if privileged && user.CanPrivileged(acl.Channel, acl.Read) {
				// 管理员可以看到所有频道
				return tx
			} else {
				// 普通用户只能看到自己及下级的频道
				return channelReadScope(c)(tx)
			}
		}).PagingList()
}

// CreateChannel 创建渠道关系
func CreateChannel(c *gin.Context) {
	if !isInChannelTree(c) {
		c.AbortWithError(http.StatusForbidden, internalChannel.ErrChannelNotInChannelTree)
		return
	}

	var json struct {
		ParentUserID uint64 `json:"parent_user_id,string"`
		ChildUserID  uint64 `json:"child_user_id,string"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	// 调用internal层创建渠道关系
	err := internalChannel.CreateChannelRelation(
		json.ParentUserID,
		json.ChildUserID,
	)

	if err == internalChannel.ErrCircleSubordinate {
		c.JSON(http.StatusNotAcceptable, gin.H{
			"message": "circle subordinate",
		})
		return
	}

	if err == internalChannel.ErrChannelDuplicate {
		c.JSON(http.StatusNotAcceptable, gin.H{
			"message": "channel duplicate",
		})
		return
	}

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}

// CreateChannelsBatch 批量创建渠道（根节点）
func CreateChannelsBatch(c *gin.Context) {
	user := api.CurrentUser(c)

	// 检查是否有全局权限
	if !user.CanPrivileged(acl.Channel, acl.Write) {
		c.AbortWithError(http.StatusForbidden, errors.New("需要全局渠道写权限"))
		return
	}

	var json struct {
		UserIDs []string `json:"user_ids" binding:"required"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	userIDs := make([]uint64, 0, len(json.UserIDs))

	for _, v := range json.UserIDs {
		userIDs = append(userIDs, cast.ToUint64(v))
	}

	// 调用internal层批量创建渠道
	err := internalChannel.CreateChannelsBatch(userIDs)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
		"count":   len(json.UserIDs),
	})
}

// DestroyChannelRelation 删除渠道关系
func DestroyChannelRelation(c *gin.Context) {
	var json struct {
		UserID uint64 `json:"user_id,string"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	// 调用internal层删除渠道关系
	err := internalChannel.DestroyChannelRelation(json.UserID)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}
