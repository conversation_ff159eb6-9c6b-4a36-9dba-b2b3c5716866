import type { StdTableColumn } from '@uozi-admin/curd'
import { userApi } from '~/api/user'
import { UserChannelType } from '~/constants'
import { userColumns } from '~/pages/users/columns'

export const channelColumns: StdTableColumn[] = [{
  title: () => $gettext('Member Name'),
  dataIndex: ['user', 'name'],
  pure: true,
  search: {
    type: 'input',
    formItem: {
      name: 'name',
    },
  },
  edit: {
    type: 'selector',
    selector: {
      getListApi: () => userApi.getPublicUsers({ channel_type: UserChannelType.Normal }),
      columns: userColumns,
    },
  },
}, {
  title: () => $gettext('Phone'),
  dataIndex: ['user', 'phone'],
  pure: true,
}, {
  title: () => $gettext('Email'),
  dataIndex: ['user', 'email'],
  pure: true,
}, {
  // 上级
  title: () => $gettext('Superior'),
  dataIndex: ['parent_user', 'name'],
  pure: true,
  customRender: ({ text }) => text || '/',
  hiddenInEdit: true,
}, {
  title: () => $gettext('Action'),
  dataIndex: 'actions',
}]
