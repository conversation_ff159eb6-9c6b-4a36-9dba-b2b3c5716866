<script setup lang="ts">
import type { Channel } from '~/api/channel'
import type { User } from '~/api/user'
import { StdCurd, StdSelector } from '@uozi-admin/curd'
import { message } from 'ant-design-vue'
import { isArray } from 'lodash-es'
import { channelApi } from '~/api/channel'
import { userApi } from '~/api/user'
import { usePermissionStore, useUserStore } from '~/store'
import { channelColumns } from './channelColumns'
import ChannelEditor from './components/ChannelEditor.vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const channelId = computed(() => route.query.channel_id as string | undefined)

function handleEdit(record: Channel) {
  router.push({
    query: {
      ...route.query,
      channel_id: record.user_id,
    },
  })
}

const privileged = computed(() => route.meta?.privileged)

const columns = computed(() => {
  if (privileged.value) {
    return channelColumns
  }
  return channelColumns.filter(column =>
    column.dataIndex !== 'actions'
    && isArray(column.dataIndex)
    && column.dataIndex.join('.') !== 'relate_user.name')
})

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()

function handleMyChannelTree() {
  router.push({
    query: { ...route.query, channel_id: userStore.info.id },
  })
}

// 添加渠道相关状态
const addChannelModalVisible = ref(false)
const selectedUsers = ref<string[]>([])
const stdCurdRef = ref()
const loading = ref(false)

// 用户选择器的列配置
const userColumns = [
  {
    dataIndex: 'name',
    title: () => $gettext('Name'),
    pure: true,
  },
  {
    dataIndex: 'email',
    title: () => $gettext('Email'),
    pure: true,
  },
  {
    dataIndex: 'phone',
    title: () => $gettext('Phone'),
    pure: true,
  },
]

// 处理用户选择
function handleUserSelected(records: User[]) {
  selectedUsers.value = records.map(user => user.id)
}

// 打开添加渠道模态框
function handleAddChannel() {
  addChannelModalVisible.value = true
  selectedUsers.value = []
}

// 确认添加渠道
async function handleConfirmAddChannel() {
  if (selectedUsers.value.length === 0) {
    message.warning($gettext('Please select at least one user'))
    return
  }

  loading.value = true
  try {
    await channelApi.createChannelsBatch({
      user_ids: selectedUsers.value,
    })

    message.success($gettext('Channels created successfully'))
    addChannelModalVisible.value = false
    selectedUsers.value = []

    // 刷新列表
    if (stdCurdRef.value) {
      stdCurdRef.value.refresh()
    }
  }
  catch {
    message.error($gettext('Failed to create channels'))
  }
  finally {
    loading.value = false
  }
}

// 取消添加渠道
function handleCancelAddChannel() {
  addChannelModalVisible.value = false
  selectedUsers.value = []
}
</script>

<template>
  <div>
    <StdCurd
      ref="stdCurdRef"
      :columns="columns"
      :api="channelApi"
      :overwrite-params="{
        privileged,
      }"
      disable-add
      disable-edit
      disable-delete
      disable-view
      disable-trash
      disable-export
    >
      <template #beforeListActions>
        <AButton
          v-if="!privileged"
          type="link"
          size="small"
          @click="handleMyChannelTree"
        >
          {{ $gettext('My Team') }}
        </AButton>
        <AButton
          v-if="privileged && actionMap.write"
          type="link"
          size="small"
          @click="handleAddChannel"
        >
          {{ $gettext('Add Channel') }}
        </AButton>
      </template>
      <template #beforeActions="{ record }">
        <AButton
          v-if="actionMap.write"
          type="link"
          size="small"
          @click="handleEdit(record)"
        >
          {{ $gettext('Edit') }}
        </AButton>
      </template>
    </StdCurd>

    <!-- 添加渠道模态框 -->
    <AModal
      v-model:open="addChannelModalVisible"
      :title="$gettext('Add Channel')"
      :confirm-loading="loading"
      @ok="handleConfirmAddChannel"
      @cancel="handleCancelAddChannel"
    >
      <div class="mb-4">
        <div class="mb-2 font-medium">
          {{ $gettext('Select Users') }}
        </div>
        <StdSelector
          v-model:value="selectedUsers"
          :get-list-api="userApi.getPublicUsers"
          :columns="userColumns"
          value-key="id"
          display-key="name"
          selection-type="checkbox"
          @selected-records="handleUserSelected"
        />
      </div>
      <div
        v-if="selectedUsers.length > 0"
        class="text-sm text-gray-600"
      >
        {{ $gettext('Selected %{count} users', { count: selectedUsers.length.toString() }) }}
      </div>
    </AModal>

    <ChannelEditor
      v-if="actionMap.write"
      :privileged="privileged"
      :open="channelId !== undefined"
      :channel-id="channelId"
    />
  </div>
</template>

<style scoped>
:deep(.tree-org-node__content .tree-org-node__inner) {
  background-color: transparent;
  box-shadow: none;
}
</style>
