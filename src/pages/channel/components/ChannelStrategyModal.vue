<script setup lang="ts">
import { StdForm } from '@uozi-admin/curd'
import { type FormInstance } from 'ant-design-vue'
import type { ChannelCommissionT } from '~/api/channel'
import { ChannelStrategyStatusMap } from '~/constants'

interface Props {
  visible?: boolean
  data?: Partial<ChannelCommissionT>
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'save', data: Partial<ChannelCommissionT>): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: () => ({}),
})

const emit = defineEmits<Emits>()

const stdFormRef = ref<{ formRef: FormInstance }>()
const formData = ref<Partial<ChannelCommissionT>>({})

// 监听props.data变化，更新表单数据
watch(() => props.data, (newData) => {
  formData.value = { ...newData }
}, { immediate: true, deep: true })

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    stdFormRef.value?.formRef?.resetFields()
  }
})

function handleSave() {
  stdFormRef.value?.formRef?.validateFields().then(() => {
    emit('save', formData.value)
  })
}

function handleCancel() {
  emit('update:visible', false)
  emit('cancel')
}

// 表单列配置
const formColumns = computed(() => [
  {
    title: () => $gettext('Remark'),
    dataIndex: 'remark',
    edit: {
      type: 'textarea',
      textarea: {
        rows: 6,
      },
    },
  },
  {
    title: () => $gettext('Effected At'),
    dataIndex: 'effected_at',
    edit: {
      type: 'datetime',
      datetime: {
        class: '!w-60%',
      },
      formItem: {
        required: true,
      },
    },
  },
  {
    title: () => $gettext('Status'),
    dataIndex: 'status',
    edit: {
      type: 'select',
      select: {
        class: '!w-60%',
        mask: ChannelStrategyStatusMap,
      },
      formItem: {
        required: true,
      },
    },
  },
])
</script>

<template>
  <AModal
    :open="props.visible"
    :title="$gettext('Team Strategy')"
    centered
    :width="400"
    :ok-text="$gettext('Save')"
    :cancel-text="$gettext('Close')"
    destroy-on-close
    @ok="handleSave"
    @cancel="handleCancel"
    @update:open="emit('update:visible', $event)"
  >
    <StdForm
      ref="stdFormRef"
      v-model:data="formData"
      :columns="formColumns"
    />
  </AModal>
</template>
