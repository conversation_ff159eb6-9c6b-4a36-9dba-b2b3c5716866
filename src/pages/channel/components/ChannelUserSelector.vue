<script setup lang="ts">
import type { User } from '~/api/user'
import { StdSelector } from '@uozi-admin/curd'
import { channelApi } from '~/api/channel'
import { UserChannelType } from '~/constants'
import { channelColumns } from '../channelColumns'

interface Props {
  visible?: boolean
  privileged?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'user-selected', users: User[]): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  privileged: false,
})

const emit = defineEmits<Emits>()

// UI状态管理
const uiState = reactive({
  selectUserWaysVisible: false,
  userFormVisible: false,
  channelSelectorVisible: false,
})

// 监听外部visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    uiState.selectUserWaysVisible = true
  }
  else {
    // 关闭所有子模态框
    uiState.selectUserWaysVisible = false
    uiState.userFormVisible = false
    uiState.channelSelectorVisible = false
  }
})

// 监听内部状态变化，同步到外部
watch(() => uiState.selectUserWaysVisible, (newVal) => {
  if (!newVal && props.visible) {
    emit('update:visible', false)
  }
})

function handleExistedUsersClick() {
  uiState.channelSelectorVisible = true
  uiState.selectUserWaysVisible = false
}

function handleNewUsersClick() {
  uiState.userFormVisible = true
  uiState.selectUserWaysVisible = false
}

function handleSelectedRecords(users: User[]) {
  emit('user-selected', users)
  uiState.channelSelectorVisible = false
  emit('update:visible', false)
}

function handleUserFormSave(user: User) {
  emit('user-selected', [user])
  uiState.userFormVisible = false
  emit('update:visible', false)
}

function handleUserFormClose() {
  uiState.userFormVisible = false
  emit('update:visible', false)
}
</script>

<template>
  <!-- 选择用户来源模态框 -->
  <AModal
    v-model:open="uiState.selectUserWaysVisible"
    :title="$gettext('Select User From')"
    centered
    width="240px"
    :footer="false"
  >
    <div class="flex flex-col gap-4 py-4">
      <AButton @click="handleExistedUsersClick">
        {{ $gettext('Existed Users') }}
      </AButton>
      <AButton
        type="primary"
        @click="handleNewUsersClick"
      >
        {{ $gettext('New Users') }}
      </AButton>
    </div>
  </AModal>

  <!-- 现有用户选择器 -->
  <StdSelector
    v-model:visible="uiState.channelSelectorVisible"
    hide-input-container
    :modal-props="{
      mask: true,
      centered: true,
    }"
    :overwrite-params="{ privileged: props.privileged }"
    :get-list-api="() => channelApi.getList({ privileged: props.privileged })"
    :columns="channelColumns"
    @selected-records="handleSelectedRecords"
  />

  <!-- 新用户表单 -->
  <UserForm
    v-model:visible="uiState.userFormVisible"
    :channel-type="UserChannelType.Normal"
    @save="handleUserFormSave"
    @close="handleUserFormClose"
  />
</template>
