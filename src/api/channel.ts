import type { CommissionTableItem } from './commission_table'
import type { ProductSKU } from '~/api/product'
import type { User } from '~/api/user'
import type { ChannelCommissionPolicyType, ChannelStrategyStatus } from '~/constants'
import type { ProductRenewalPlan } from '~/constants/product'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface Channel extends User {
  user_id: number
  user?: User
  parent_user_id: number
  parent_user?: User
}

export interface ChannelNode {
  root_id: string
  name: string
  current_id: string
  children: ChannelNode[]
  commission_rate?: string
  type?: string
}

export interface ChannelChain {
  product_sku: ProductSKU
  path: ChannelNode[]
  type: 'main' | 'sub'
}

export const channelApi = extendCurdApi(useCurdApi<Channel>('/channels'), {
  async getChannel(params: {
    user_id: string
    privileged?: boolean
  }) {
    return http.get<ChannelNode>('/channel/tree', { params })
  },
  async removeRelation(data: { user_id: string }) {
    return http.delete('/channel_relation', { data })
  },
  async createChannelsBatch(data: { user_ids: string[] }) {
    return http.post('/channels/batch', data)
  },
})

export interface ChannelCommissionT {
  id: string
  channel_id: string
  status: ChannelStrategyStatus
  remark: string
  effected_at: number
  created_at: number
  updated_at: number
}

export interface ChannelCommissionPolicyT {
  id: number
  channel_commission_id: number
  type: ChannelCommissionPolicyType
  company_ids: number[]
  product_ids: number[]
  settlement_period: ProductRenewalPlan
  max_periods: number
  policy: Record<string, string>
  priority: number
  remark: string
  order_id: number
}

export interface ChannelCommissionTableT {
  id: number
  channel_id: number
  product_id: number
  period: number
  commission_rate: number
}

export interface ChannelCommissionTableItem extends CommissionTableItem {

}

export interface DownloadPdfParams {
  ffyap?: boolean
  fy100?: boolean
  round?: boolean
  round_pos?: number
  language?: string
  channel_id: number
  date: number
  page?: number
  product_company_id?: number[]
  product_name?: string
}

export const channelCommissionApi = extendCurdApi(useCurdApi<ChannelCommissionT>('/channel_commissions'), {
  // 可以在这里添加扩展方法
})

export const channelCommissionTableApi = extendCurdApi(useCurdApi<ChannelCommissionT>('/channel_commission_table'), {
  async downloadPdf(data: DownloadPdfParams) {
    return http.post<ArrayBuffer>('/channel_commission_table/pdf', data, {
      responseType: 'arraybuffer',
    })
  },
})

export const channelCommissionPolicyApi = extendCurdApi(useCurdApi<ChannelCommissionPolicyT>('/channel_commission_policies'), {
  async updateOrder(data: any) {
    return http.post('/channel_commission_policies/order', data)
  },
})
