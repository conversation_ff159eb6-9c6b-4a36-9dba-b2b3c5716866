package channel

import (
	"encoding/json"
	"fmt"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// BidirectionalChannelService 双向链表频道服务
type BidirectionalChannelService struct {
	db *gorm.DB
	q  *query.Query
}

// NewBidirectionalChannelService 创建双向链表频道服务实例
func NewBidirectionalChannelService() *BidirectionalChannelService {
	db := cosy.UseDB()
	return &BidirectionalChannelService{
		db: db,
		q:  query.Use(db),
	}
}

// GetChannelWithBidirectionalInfo 获取包含双向链表信息的频道记录
func (s *BidirectionalChannelService) GetChannelWithBidirectionalInfo(userID uint64) (*model.Channel, error) {
	q := s.q.Channel
	return q.Where(q.UserID.Eq(userID)).
		Preload(q.User, q.ParentUser).
		First()
}

// GetDirectChildren 高效获取直接子节点（利用 children_user_ids 字段）
func (s *BidirectionalChannelService) GetDirectChildren(parentUserID uint64) ([]*model.Channel, error) {
	// 首先获取父节点的子节点ID列表
	parentChannel, err := s.GetChannelWithBidirectionalInfo(parentUserID)
	if err != nil {
		return nil, err
	}

	if len(parentChannel.ChildrenUserIDs) == 0 {
		return []*model.Channel{}, nil
	}

	// 批量查询所有子节点
	q := s.q.Channel
	return q.Where(q.UserID.In(parentChannel.GetChildren()...)).
		Preload(q.User, q.ParentUser).
		Find()
}

// GetAllAncestors 向上遍历获取所有祖先节点
func (s *BidirectionalChannelService) GetAllAncestors(userID uint64) ([]*model.Channel, error) {
	var ancestors []*model.Channel
	currentUserID := userID

	for currentUserID != 0 {
		channel, err := s.GetChannelWithBidirectionalInfo(currentUserID)
		if err != nil {
			break
		}

		if channel.ParentUserID == 0 {
			// 到达根节点
			break
		}

		// 获取父节点
		parentChannel, err := s.GetChannelWithBidirectionalInfo(channel.ParentUserID)
		if err != nil {
			break
		}

		ancestors = append(ancestors, parentChannel)
		currentUserID = channel.ParentUserID
	}

	return ancestors, nil
}

// GetAllDescendants 递归获取所有后代节点（利用双向链表优化）
func (s *BidirectionalChannelService) GetAllDescendants(userID uint64) ([]*model.Channel, error) {
	var descendants []*model.Channel
	visited := make(map[uint64]bool)

	err := s.collectDescendants(userID, &descendants, visited)
	return descendants, err
}

// collectDescendants 递归收集后代节点
func (s *BidirectionalChannelService) collectDescendants(userID uint64, descendants *[]*model.Channel, visited map[uint64]bool) error {
	if visited[userID] {
		return nil // 防止循环引用
	}
	visited[userID] = true

	// 获取直接子节点
	children, err := s.GetDirectChildren(userID)
	if err != nil {
		return err
	}

	for _, child := range children {
		*descendants = append(*descendants, child)
		// 递归获取子节点的后代
		err = s.collectDescendants(child.UserID, descendants, visited)
		if err != nil {
			return err
		}
	}

	return nil
}

// GetPathToRoot 获取从当前节点到根节点的路径
func (s *BidirectionalChannelService) GetPathToRoot(userID uint64) ([]*model.Channel, error) {
	var path []*model.Channel
	currentUserID := userID

	for currentUserID != 0 {
		channel, err := s.GetChannelWithBidirectionalInfo(currentUserID)
		if err != nil {
			return nil, err
		}

		path = append([]*model.Channel{channel}, path...) // 前置插入，保持从根到当前的顺序

		if channel.ParentUserID == 0 {
			break // 到达根节点
		}

		currentUserID = channel.ParentUserID
	}

	return path, nil
}

// UpdateBidirectionalLinks 更新双向链表链接
func (s *BidirectionalChannelService) UpdateBidirectionalLinks(parentUserID uint64) error {
	// 获取父节点的所有子节点
	children, err := s.GetDirectChildren(parentUserID)
	if err != nil {
		return err
	}

	if len(children) == 0 {
		return nil
	}

	// 更新父节点的子节点列表和计数
	childrenIDs := make([]uint64, len(children))
	for i, child := range children {
		childrenIDs[i] = child.UserID
	}

	childrenJSON, err := json.Marshal(childrenIDs)
	if err != nil {
		return fmt.Errorf("序列化子节点列表失败: %v", err)
	}

	q := s.q.Channel
	_, err = q.Where(q.UserID.Eq(parentUserID)).
		Updates(map[string]interface{}{
			"children_user_ids": string(childrenJSON),
			"child_count":       len(children),
		})
	if err != nil {
		return err
	}

	// 更新兄弟节点的双向链表
	for i, child := range children {
		var nextSiblingID, prevSiblingID uint64

		if i > 0 {
			prevSiblingID = children[i-1].UserID
		}
		if i < len(children)-1 {
			nextSiblingID = children[i+1].UserID
		}

		_, err = q.Where(q.UserID.Eq(child.UserID)).
			Updates(map[string]interface{}{
				"next_sibling_id": nextSiblingID,
				"prev_sibling_id": prevSiblingID,
			})
		if err != nil {
			return err
		}
	}

	return nil
}
