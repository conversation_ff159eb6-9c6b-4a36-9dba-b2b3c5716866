package channel

import (
	"errors"
	"fmt"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// ChannelReadScope 返回一个GORM查询范围函数，用于限制用户只能查看自己及下级渠道的数据
// 如果用户有特权且请求包含privileged参数，则不限制查询范围
func ChannelReadScope(userID uint64, canPrivileged bool, privileged bool) func(tx *gorm.DB) *gorm.DB {
	if privileged && canPrivileged {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	return func(tx *gorm.DB) *gorm.DB {
		// 获取当前用户及其所有下级节点的ID列表
		tree, err := GetChannelChildren(userID)
		userIds := []uint64{}

		if err == nil && tree != nil {
			// 递归获取所有下级节点的ID
			allUserIds := tree.GetAllDescendantIDs()
			if len(allUserIds) > 0 {
				userIds = allUserIds
			}
		}

		return tx.
			Where("user_id IN ?", userIds).
			Where("user_id != ?", userID)
	}
}

// IsUserInChannelTree 检查指定的渠道ID是否在用户的渠道树中
func IsUserInChannelTree(userID uint64, channelID uint64, hasGlobalPermission bool) bool {
	// 如果有全局权限，则直接返回true
	if hasGlobalPermission {
		return true
	}

	var ids []uint64
	// 获取渠道树
	tree, err := GetChannelTree(userID)
	if err != nil {
		ids = append(ids, userID)
	} else {
		// 获取当前用户及其所有下级节点的ID
		ids = tree.GetAllDescendantIDs()
	}

	// 判断请求的渠道是否在树中
	return lo.Contains(ids, channelID)
}

// GetChannelByID 根据ID获取渠道信息
func GetChannelByID(id uint64, scope func(tx *gorm.DB) *gorm.DB) (*model.User, error) {
	userChannel := &model.User{}
	db := cosy.UseDB()

	err := db.Model(&model.User{}).
		Scopes(scope).
		Joins("LEFT JOIN channels ON channels.user_id = users.id").
		Where("users.id = ?", id).
		Scan(userChannel).
		Error

	return userChannel, err
}

// ModifyChannelByID 更新渠道信息
func ModifyChannelByID(id uint64) error {
	q := query.Channel

	_, err := q.Where(q.UserID.Eq(id)).FirstOrCreate()
	return err
}

// CreateChannelRelation 创建渠道关系（支持双向链表）
func CreateChannelRelation(parentUserID uint64, childUserID uint64) error {
	service := NewBidirectionalChannelService()
	q := service.q.Channel
	db := service.db

	// 防止循环路径
	if parentUserID == childUserID {
		return ErrCircleSubordinate
	}

	// 检查是否会形成循环引用
	ancestors, err := service.GetAllAncestors(parentUserID)
	if err == nil {
		for _, ancestor := range ancestors {
			if ancestor.UserID == childUserID {
				return ErrCircleSubordinate
			}
		}
	}

	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查子用户是否已经有父节点
	existingChild, err := q.Where(q.UserID.Eq(childUserID)).First()
	if err != nil {
		// 如果子用户没有频道记录，创建一个
		newChannel := &model.Channel{
			UserID:       childUserID,
			ParentUserID: parentUserID,
		}
		if err := tx.Create(newChannel).Error; err != nil {
			tx.Rollback()
			return err
		}
	} else {
		// 如果子用户已经有父节点，返回错误
		if existingChild.ParentUserID != 0 {
			tx.Rollback()
			return ErrChannelDuplicate
		}

		// 更新子用户的父节点
		if _, err = q.Where(q.UserID.Eq(childUserID)).Update(q.ParentUserID, parentUserID); err != nil {
			tx.Rollback()
			return err
		}
	}

	// 更新父节点的双向链表信息
	if err := service.UpdateBidirectionalLinks(parentUserID); err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}

// DestroyChannelRelation 删除渠道关系（支持双向链表）
func DestroyChannelRelation(userID uint64) error {
	service := NewBidirectionalChannelService()
	q := service.q.Channel
	db := service.db

	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查找要删除的节点
	nodeToDelete, err := service.GetChannelWithBidirectionalInfo(userID)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 不允许删除根节点
	if nodeToDelete.IsRoot() {
		tx.Rollback()
		return ErrCannotRemoveRootNode
	}

	// 获取被删除节点的父节点ID
	parentUserID := nodeToDelete.ParentUserID

	// 获取被删除节点的所有直接子节点
	childNodes, err := service.GetDirectChildren(userID)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 将被删除节点的所有直接子节点重新关联到其父节点
	for _, child := range childNodes {
		_, err = q.Where(q.UserID.Eq(child.UserID)).Update(q.ParentUserID, parentUserID)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 删除目标节点（设置 parent_user_id = 0，表示移除关系）
	_, err = q.Where(q.UserID.Eq(userID)).Updates(map[string]interface{}{
		"parent_user_id":    0,
		"next_sibling_id":   0,
		"prev_sibling_id":   0,
		"children_user_ids": nil,
		"child_count":       0,
	})
	if err != nil {
		tx.Rollback()
		return err
	}

	// 更新原父节点的双向链表信息
	if err := service.UpdateBidirectionalLinks(parentUserID); err != nil {
		tx.Rollback()
		return err
	}

	// 如果有子节点被重新分配，也需要更新新父节点的双向链表信息
	if len(childNodes) > 0 {
		if err := service.UpdateBidirectionalLinks(parentUserID); err != nil {
			tx.Rollback()
			return err
		}
	}

	tx.Commit()
	return nil
}

// CreateChannelsBatch 批量创建渠道（根节点）
func CreateChannelsBatch(userIDs []uint64) error {
	if len(userIDs) == 0 {
		return errors.New("用户ID列表不能为空")
	}

	db := cosy.UseDB()
	q := query.Use(db)

	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, userID := range userIDs {
		// 检查用户是否存在
		userQuery := q.User
		_, err := userQuery.Where(userQuery.ID.Eq(userID)).First()
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("用户ID %d 不存在", userID)
		}

		// 检查是否已经有渠道记录
		channelQuery := q.Channel
		existingChannel, err := channelQuery.Where(channelQuery.UserID.Eq(userID)).First()
		if err == nil && existingChannel != nil {
			// 如果已经存在渠道记录，跳过
			continue
		}

		// 创建新的根节点渠道记录
		newChannel := &model.Channel{
			UserID:          userID,
			ParentUserID:    0,                       // 根节点
			ChildrenUserIDs: model.ChildrenUserIDs{}, // 空数组
			ChildCount:      0,
		}

		if err := tx.Create(newChannel).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建用户ID %d 的渠道记录失败: %v", userID, err)
		}
	}

	return tx.Commit().Error
}
