package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// ChildrenUserIDs 自定义类型用于处理 JSON 数组
type ChildrenUserIDs []uint64

// Scan 实现 sql.Scanner 接口
func (c *ChildrenUserIDs) Scan(value interface{}) error {
	if value == nil {
		*c = ChildrenUserIDs{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, c)
	case string:
		return json.Unmarshal([]byte(v), c)
	default:
		return fmt.Errorf("无法将 %T 转换为 ChildrenUserIDs", value)
	}
}

// Value 实现 driver.Valuer 接口
func (c ChildrenUserIDs) Value() (driver.Value, error) {
	if len(c) == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

// Channel 频道模型 - 使用树形结构
type Channel struct {
	Model
	UserID          uint64          `json:"user_id,string,omitempty" gorm:"uniqueIndex;comment:用户ID"`
	User            *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	ParentUserID    uint64          `json:"parent_user_id,string,omitempty" gorm:"index;comment:父用户ID"`
	ParentUser      *User           `json:"parent_user,omitempty" gorm:"foreignKey:ParentUserID"`
	ChildrenUserIDs ChildrenUserIDs `json:"children_user_ids,omitempty" gorm:"type:json;comment:子用户ID列表"`
	ChildCount      int             `json:"child_count,omitempty" gorm:"comment:子节点数量"`
}

// GetChildren 获取所有直接子节点的用户ID
func (c *Channel) GetChildren() []uint64 {
	return []uint64(c.ChildrenUserIDs)
}

// HasChildren 检查是否有子节点
func (c *Channel) HasChildren() bool {
	return c.ChildCount > 0
}

// IsRoot 检查是否为根节点
func (c *Channel) IsRoot() bool {
	return c.ParentUserID == 0
}

// AddChild 添加子节点ID到列表中
func (c *Channel) AddChild(childUserID uint64) {
	// 检查是否已存在
	for _, id := range c.ChildrenUserIDs {
		if id == childUserID {
			return
		}
	}
	c.ChildrenUserIDs = append(c.ChildrenUserIDs, childUserID)
	c.ChildCount = len(c.ChildrenUserIDs)
}

// RemoveChild 从子节点列表中移除指定ID
func (c *Channel) RemoveChild(childUserID uint64) {
	for i, id := range c.ChildrenUserIDs {
		if id == childUserID {
			c.ChildrenUserIDs = append(c.ChildrenUserIDs[:i], c.ChildrenUserIDs[i+1:]...)
			c.ChildCount = len(c.ChildrenUserIDs)
			return
		}
	}
}
